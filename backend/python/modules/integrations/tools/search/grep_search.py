import os
import re
from typing import List, Tuple, Optional
import subprocess
import xml.etree.ElementTree as ET
import fnmatch
import asyncio
from pathlib import Path

from modules.common.schema import CodeSnippet
from modules.integrations.tools.search.search_tool import SearchToolABC
from core.config import get_config
from utils.trace_logger import get_trace_logger

# 获取支持 trace 的 logger
logger = get_trace_logger(__name__)

from utils.file import should_ignore_path

class GrepSearchTool(SearchToolABC):
    """基于ripgrep的搜索引擎"""
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        self.file_filter = get_config().file_filter



    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        使用ripgrep搜索代码片段，支持XML格式的查询参数

        Args:
            query: 查询字符串，可以是XML格式或普通文本

        Returns:
            List[CodeSnippet]: 搜索结果
        """
        snippets = []

        try:
            # 解析查询参数
            search_params = self._parse_query(query)

            # 构建ripgrep命令
            cmd = self._build_ripgrep_command(search_params)
            logger.info(f"CMD: {cmd}")

            # 执行搜索
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                snippets = self._parse_ripgrep_output(result.stdout)
            elif result.returncode == 1:
                # ripgrep返回1表示没有找到匹配，这是正常情况
                logger.info("No matches found")
            else:
                logger.warning(f"Ripgrep command failed with return code {result.returncode}: {result.stderr}")

        except Exception as e:
            logger.error(f"Ripgrep search failed: {e}")

        return snippets

    async def search_async(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        使用ripgrep搜索代码片段的异步版本

        Args:
            query: 查询字符串，可以是XML格式或普通文本

        Returns:
            List[CodeSnippet]: 搜索结果
        """
        snippets = []

        try:
            # 解析查询参数
            search_params = self._parse_query(query)

            # 构建ripgrep命令
            cmd = self._build_ripgrep_command(search_params)
            logger.info(f"CMD: {cmd}")

            # 异步执行搜索
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                # 将bytes解码为字符串
                stdout_str = stdout.decode('utf-8', errors='ignore')
                snippets = self._parse_ripgrep_output(stdout_str)
            elif process.returncode == 1:
                # ripgrep返回1表示没有找到匹配，这是正常情况
                logger.info("No matches found")
            else:
                # 将stderr解码为字符串用于日志记录
                stderr_str = stderr.decode('utf-8', errors='ignore')
                logger.warning(f"Ripgrep command failed with return code {process.returncode}: {stderr_str}")

        except Exception as e:
            logger.error(f"Async ripgrep search failed: {e}")

        return snippets

    def _parse_query(self, query: str) -> dict:
        """
        解析查询参数，支持XML格式和普通文本

        Args:
            query: 查询字符串

        Returns:
            dict: 解析后的参数字典
        """
        # 默认参数
        params = {
            'path': '.',  # 默认为当前目录，相对于repo_path
            'regex': query.strip(),
            'file_pattern': None
        }

        # 尝试解析XML格式
        query_stripped = query.strip()

        try:
            # 解析XML
            root = ET.fromstring(query_stripped)

            # 提取参数
            path_elem = root.find('path')
            if path_elem is not None and path_elem.text:
                # 处理相对路径
                path = path_elem.text.strip()
                params['path'] = path  # 保存原始路径，在build_command中处理

            regex_elem = root.find('regex')
            if regex_elem is not None and regex_elem.text:
                params['regex'] = regex_elem.text.strip()

            file_pattern_elem = root.find('file_pattern')
            if file_pattern_elem is not None and file_pattern_elem.text:
                params['file_pattern'] = file_pattern_elem.text.strip()

            logger.debug(f"Successfully parsed XML query: path={params['path']}, regex={params['regex']}, file_pattern={params['file_pattern']}")

        except ET.ParseError as e:
            logger.warning(f"Failed to parse XML query, using as plain text: {e}")
            logger.debug(f"Failed XML content: {query_stripped}")

        return params

    def _build_ripgrep_command(self, params: dict) -> List[str]:
        """
        构建ripgrep命令

        Args:
            params: 搜索参数

        Returns:
            List[str]: ripgrep命令参数列表
        """
        cmd = [
            "rg",
            "--line-number",  # 显示行号
            "--context", "3",  # 显示上下文3行
            "--color", "never",  # 不使用颜色输出
        ]

        # 处理正则表达式中的大小写不敏感标志
        regex = params['regex']
        if regex.startswith('(?i)'):
            cmd.append('--ignore-case')  # 忽略大小写
            regex = regex[4:]  # 移除(?i)前缀

        # 处理搜索路径
        search_path = params['path']
        if search_path == '.':
            # 当前目录，使用repo_path
            final_path = self.repo_path
        elif os.path.isabs(search_path):
            # 绝对路径，直接使用
            final_path = search_path
        else:
            # 相对路径，相对于repo_path
            final_path = os.path.join(self.repo_path, search_path)

        # 添加文件模式过滤
        if params['file_pattern']:
            cmd.extend(["--glob", params['file_pattern']])
        else:
            # 使用配置文件中的文件类型过滤
            for ext in self.file_filter.local_include:
                cmd.extend(["--glob", f"*{ext}"])

        # 添加正则表达式和搜索路径
        cmd.append(regex)
        cmd.append(final_path)

        return cmd

    def _parse_ripgrep_output(self, output: str) -> List[CodeSnippet]:
        """
        解析ripgrep输出结果

        Args:
            output: ripgrep命令输出

        Returns:
            List[CodeSnippet]: 解析后的代码片段
        """
        snippets = []
        current_file = None
        current_lines = []

        for line in output.split('\n'):
            if not line.strip():
                # 空行表示结束
                if current_file and current_lines:
                    snippets.extend(self._extract_snippets_from_lines(current_file, current_lines))
                    current_lines = []
                current_file = None
                continue

            # ripgrep输出格式：文件路径:行号:内容 或 文件路径-行号-内容（上下文）
            # 匹配行格式：/path/to/file.py:157:    def executemany(self, query, args):
            match = re.match(r'^([^:]+):(\d+):(.*)$', line)
            # 上下文行格式：/path/to/file.py-158-        """Run several data against one query.
            context_match = re.match(r'^([^:]+)-(\d+)-(.*)$', line)

            if match:
                file_path, line_number, content = match.groups()
                if not should_ignore_path(file_path):
                    if current_file != file_path:
                        # 新文件，处理之前的文件
                        if current_file and current_lines:
                            snippets.extend(self._extract_snippets_from_lines(current_file, current_lines))
                            current_lines = []
                        current_file = file_path
                    current_lines.append((int(line_number), content, True))  # True表示匹配行
            elif context_match:
                file_path, line_number, content = context_match.groups()
                if not should_ignore_path(file_path):
                    # 如果还没有设置当前文件，或者文件路径匹配
                    if current_file is None:
                        current_file = file_path
                    if current_file == file_path:
                        current_lines.append((int(line_number), content, False))  # False表示上下文行

        # 处理最后一组
        if current_file and current_lines:
            snippets.extend(self._extract_snippets_from_lines(current_file, current_lines))

        return snippets
    
    def _extract_snippets_from_lines(self, file_path: str, lines: List[Tuple[int, str, bool]]) -> List[CodeSnippet]:
        """
        从行数据中提取代码片段，智能合并相近的匹配

        Args:
            file_path: 文件路径
            lines: 行数据列表 (行号, 内容, 是否匹配行)

        Returns:
            List[CodeSnippet]: 代码片段列表
        """
        snippets = []

        if not lines:
            return snippets

        # 按行号排序，确保顺序正确
        lines.sort(key=lambda x: x[0])

        # 找到所有匹配行
        match_indices = [i for i, (_, _, is_match) in enumerate(lines) if is_match]

        if not match_indices:
            return snippets

        # 根据匹配密度决定是否分组
        if len(match_indices) == 1:
            # 单个匹配，使用原来的简单逻辑
            snippet = self._create_simple_snippet(file_path, lines, match_indices[0])
            if snippet:
                snippets.append(snippet)
        else:
            # 多个匹配，使用智能分组
            groups = self._group_nearby_matches(lines, match_indices)

            for group in groups:
                if len(group) == 1:
                    # 单个匹配的组，使用简单逻辑
                    snippet = self._create_simple_snippet(file_path, lines, group[0])
                else:
                    # 多个匹配的组，使用合并逻辑
                    snippet = self._create_snippet_from_group(file_path, lines, group)

                if snippet:
                    snippets.append(snippet)

        return snippets

    def _create_simple_snippet(self, file_path: str, lines: List[Tuple[int, str, bool]], match_idx: int) -> Optional[CodeSnippet]:
        """
        为单个匹配行创建简单的代码片段

        Args:
            file_path: 文件路径
            lines: 行数据列表
            match_idx: 匹配行索引

        Returns:
            Optional[CodeSnippet]: 代码片段
        """
        line_number, content, _ = lines[match_idx]

        # 收集前面的上下文
        context_before = []
        context_before_lines = []
        for i in range(match_idx - 1, -1, -1):
            prev_line_num, prev_content, prev_is_match = lines[i]
            if not prev_is_match:
                context_before.insert(0, prev_content)
                context_before_lines.insert(0, prev_line_num)
            else:
                break

        # 收集后面的上下文
        context_after = []
        context_after_lines = []
        for i in range(match_idx + 1, len(lines)):
            next_line_num, next_content, next_is_match = lines[i]
            if not next_is_match:
                context_after.append(next_content)
                context_after_lines.append(next_line_num)
            else:
                break

        # 计算实际的起始和结束行号
        start_line = context_before_lines[0] if context_before_lines else line_number
        end_line = context_after_lines[-1] if context_after_lines else line_number

        return CodeSnippet(
            file_path=os.path.relpath(file_path, self.repo_path),
            start_line=start_line,
            end_line=end_line,
            content=content,
            context_before="\n".join(context_before),
            context_after="\n".join(context_after)
        )

    def _group_nearby_matches(self, lines: List[Tuple[int, str, bool]], match_indices: List[int], max_gap: int = 30) -> List[List[int]]:
        """
        将相近的匹配行分组

        Args:
            lines: 行数据列表
            match_indices: 匹配行的索引列表
            max_gap: 最大行号间隔，超过此间隔的匹配行将分为不同组

        Returns:
            List[List[int]]: 分组后的匹配行索引列表
        """
        if not match_indices:
            return []

        groups = []
        current_group = [match_indices[0]]

        for i in range(1, len(match_indices)):
            current_match_line = lines[match_indices[i]][0]
            prev_match_line = lines[match_indices[i-1]][0]

            # 如果行号间隔小于等于max_gap，加入当前组
            if current_match_line - prev_match_line <= max_gap:
                current_group.append(match_indices[i])
            else:
                # 否则开始新组
                groups.append(current_group)
                current_group = [match_indices[i]]

        # 添加最后一组
        groups.append(current_group)

        return groups

    def _create_snippet_from_group(self, file_path: str, lines: List[Tuple[int, str, bool]], group: List[int]) -> Optional[CodeSnippet]:
        """
        从一组匹配行创建代码片段

        Args:
            file_path: 文件路径
            lines: 行数据列表
            group: 匹配行索引组

        Returns:
            Optional[CodeSnippet]: 代码片段，如果无法创建则返回None
        """
        if not group:
            return None

        # 找到组的范围
        first_match_idx = group[0]
        last_match_idx = group[-1]

        # 收集所有相关行（包括组内的上下文）
        start_idx = first_match_idx
        end_idx = last_match_idx

        # 向前扩展到上下文开始
        while start_idx > 0 and not lines[start_idx - 1][2]:  # 不是匹配行
            start_idx -= 1

        # 向后扩展到上下文结束
        while end_idx < len(lines) - 1 and not lines[end_idx + 1][2]:  # 不是匹配行
            end_idx += 1

        # 提取相关行
        relevant_lines = lines[start_idx:end_idx + 1]

        # 分离匹配行和上下文行
        match_lines = []
        context_lines = []

        for line_num, content, is_match in relevant_lines:
            if is_match:
                match_lines.append((line_num, content))
            else:
                context_lines.append((line_num, content))

        if not match_lines:
            return None

        # 计算范围
        all_line_nums = [line_num for line_num, _, _ in relevant_lines]
        start_line = min(all_line_nums)
        end_line = max(all_line_nums)

        # 构建内容：如果有多个匹配行，用换行符连接
        if len(match_lines) == 1:
            content = match_lines[0][1]
        else:
            content = "\n".join([f"Line {line_num}: {content}" for line_num, content in match_lines])

        # 构建上下文
        context_before_lines = [(line_num, content) for line_num, content in context_lines if line_num < match_lines[0][0]]
        context_after_lines = [(line_num, content) for line_num, content in context_lines if line_num > match_lines[-1][0]]

        context_before = "\n".join([content for _, content in context_before_lines])
        context_after = "\n".join([content for _, content in context_after_lines])

        return CodeSnippet(
            file_path=os.path.relpath(file_path, self.repo_path),
            start_line=start_line,
            end_line=end_line,
            content=content,
            context_before=context_before,
            context_after=context_after
        )

    @property
    def description(self):
        return """-`grep`: Perform a powerful regex-based search across multiple files within a directory tree. This tool recursively searches through files, finds pattern matches, and returns each match with surrounding context lines for better understanding. Ideal for code analysis, finding specific patterns, locating function definitions, or searching for text across large codebases.

Parameters:
- path: (required) The target directory path to search in, relative to the current working directory. The search will recursively include all subdirectories. Use "." for current directory.
- regex: (required) The regular expression pattern to search for. Uses extended grep regex syntax (POSIX ERE). Supports:
  - Basic patterns: `function`, `class MyClass`
  - Case-insensitive: `(?i)pattern` (special prefix handled by tool)
  - Word boundaries: `\\bword\\b`
  - Character classes: `[a-zA-Z]`, `[0-9]`
  - Quantifiers: `+`, `*`, `?`, `{n,m}`
  - Alternation: `pattern1|pattern2`
  - Grouping: `(pattern)`
- file_pattern: (optional) Glob pattern to filter which files to search. Examples:
  - `*.js` - only JavaScript files
  - `*.{ts,tsx}` - TypeScript files
  - `**/*.py` - Python files in all subdirectories
  - If omitted, uses configured file extensions

Note: Results will show each match with surrounding context lines. Large result sets may be truncated."""
    
    @property
    def examples(self):
        return """<output>
    <grep>
    <path>src</path>
    <regex>function\\s+(\\w+)</regex>
    <file_pattern>*.js</file_pattern>
    </grep>

    <grep>
    <path>.</path>
    <regex>(?i)todo|fixme</regex>
    </grep>
</output>
"""

if __name__ == "__main__":
    # 测试用例
    grep_search_tool = GrepSearchTool("/Users/<USER>/01-Projects/Codebase-Dev/backend/python/data/repos/PyMySQL")

    # 测试1: 简单正则表达式
    print("=== 测试1: 简单正则表达式 ===")
    query1 = "<grep><path>pymysql</path><regex>def executemany</regex><file_pattern>*.py</file_pattern></grep>"
    print(f"测试查询: {query1}")

    snippets1 = grep_search_tool.search(query1)
    print(f"找到 {len(snippets1)} 个结果")

    if snippets1:
        print("第一个结果:")
        print(f"文件: {snippets1[0].file_path}")
        print(f"行号: {snippets1[0].start_line}")
        print(f"内容: {snippets1[0].content}")

    print("\n=== 测试2: 复杂正则表达式（非贪婪匹配） ===")
    # 测试2: 复杂正则表达式，ripgrep原生支持
    query2 = "<grep><path>pymysql</path><regex>def\\s+\\w+.*?\\(.*?\\):</regex><file_pattern>*.py</file_pattern></grep>"
    print(f"测试查询: {query2}")

    snippets2 = grep_search_tool.search(query2)
    print(f"找到 {len(snippets2)} 个结果")

    if snippets2:
        print("前3个结果:")
        for i, snippet in enumerate(snippets2[:3]):
            print(f"{i+1}. 文件: {snippet.file_path}, 行号: {snippet.start_line}")
            print(f"   内容: {snippet.content}")

    print("\n=== 测试3: 非捕获组 ===")
    # 测试3: 非捕获组，ripgrep原生支持
    query3 = "<grep><path>pymysql</path><regex>(?:class|def)\\s+(\\w+)</regex><file_pattern>*.py</file_pattern></grep>"
    print(f"测试查询: {query3}")

    snippets3 = grep_search_tool.search(query3)
    print(f"找到 {len(snippets3)} 个结果")

    if snippets3:
        print("前3个结果:")
        for i, snippet in enumerate(snippets3[:3]):
            print(f"{i+1}. 文件: {snippet.file_path}, 行号: {snippet.start_line}")
            print(f"   内容: {snippet.content}")
