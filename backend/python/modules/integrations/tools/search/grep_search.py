import os
import re
from typing import List, Tuple, Optional
import subprocess
import xml.etree.ElementTree as ET
import fnmatch
import asyncio
from pathlib import Path

from modules.common.schema import CodeSnippet
from modules.integrations.tools.search.search_tool import SearchToolABC
from core.config import get_config
from utils.trace_logger import get_trace_logger

# 获取支持 trace 的 logger
logger = get_trace_logger(__name__)

from utils.file import should_ignore_path

class GrepSearchTool(SearchToolABC):
    """基于grep的搜索引擎"""
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        self.file_filter = get_config().file_filter

        # 定义grep不支持的复杂正则表达式特性
        self.complex_regex_patterns = [
            r'\?\?',      # 非贪婪量词 *?, +?, ??
            r'\*\?',      # 非贪婪量词
            r'\+\?',      # 非贪婪量词
            r'\{\d+,?\d*\}\?',  # 非贪婪量词 {n,m}?
            r'\(\?\:',    # 非捕获组 (?:...)
            r'\(\?\=',    # 正向先行断言 (?=...)
            r'\(\?\!',    # 负向先行断言 (?!...)
            r'\(\?\<\=', # 正向后行断言 (?<=...)
            r'\(\?\<\!', # 负向后行断言 (?<!...)
            r'\\[bBAZzGK]',  # 单词边界等高级特性
        ]

    def _is_complex_regex(self, regex: str) -> bool:
        """
        检测正则表达式是否包含grep不支持的复杂特性

        Args:
            regex: 正则表达式字符串

        Returns:
            bool: 如果包含复杂特性返回True
        """
        for pattern in self.complex_regex_patterns:
            if re.search(pattern, regex):
                logger.debug(f"Detected complex regex pattern: {pattern} in {regex}")
                return True
        return False

    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        使用grep搜索代码片段，支持XML格式的查询参数

        Args:
            query: 查询字符串，可以是XML格式或普通文本

        Returns:
            List[CodeSnippet]: 搜索结果
        """
        snippets = []

        try:
            # 解析查询参数
            search_params = self._parse_query(query)

            # 检查是否包含复杂正则表达式
            if self._is_complex_regex(search_params['regex']):
                logger.info("Using Python regex engine for complex pattern")
                snippets = self._python_regex_search(search_params)
            else:
                logger.info("Using grep for simple pattern")
                # 构建grep命令
                cmd = self._build_grep_command(search_params)
                logger.info(f"CMD: {cmd}")

                # 执行搜索
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    errors='ignore'
                )

                if result.returncode == 0:
                    snippets = self._parse_grep_output(result.stdout)
                elif result.returncode == 1:
                    # grep返回1表示没有找到匹配，这是正常情况
                    logger.info("No matches found")
                else:
                    logger.warning(f"Grep command failed with return code {result.returncode}: {result.stderr}")

        except Exception as e:
            logger.error(f"Grep search failed: {e}")

        return snippets

    def _python_regex_search(self, params: dict) -> List[CodeSnippet]:
        """
        使用Python的re模块进行复杂正则表达式搜索

        Args:
            params: 搜索参数

        Returns:
            List[CodeSnippet]: 搜索结果
        """
        snippets = []

        try:
            # 编译正则表达式
            regex_pattern = params['regex']
            flags = 0

            # 处理大小写不敏感标志
            if regex_pattern.startswith('(?i)'):
                flags |= re.IGNORECASE
                regex_pattern = regex_pattern[4:]

            compiled_regex = re.compile(regex_pattern, flags)

            # 确定搜索路径
            search_path = params['path']
            if search_path == '.':
                final_path = self.repo_path
            elif os.path.isabs(search_path):
                final_path = search_path
            else:
                final_path = os.path.join(self.repo_path, search_path)

            # 获取文件列表
            files_to_search = self._get_files_to_search(final_path, params['file_pattern'])

            # 搜索每个文件
            for file_path in files_to_search:
                if should_ignore_path(file_path):
                    continue

                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    # 按行分割内容进行搜索
                    lines = content.split('\n')
                    for line_num, line in enumerate(lines, 1):
                        if compiled_regex.search(line):
                            # 获取上下文
                            context_before = []
                            context_after = []

                            # 上下文行数
                            context_lines = 3

                            # 获取前面的上下文
                            for i in range(max(0, line_num - context_lines - 1), line_num - 1):
                                context_before.append(lines[i])

                            # 获取后面的上下文
                            for i in range(line_num, min(len(lines), line_num + context_lines)):
                                context_after.append(lines[i])

                            snippet = CodeSnippet(
                                file_path=os.path.relpath(file_path, self.repo_path),
                                start_line=line_num,
                                end_line=line_num,
                                content=line,
                                context_before="\n".join(context_before),
                                context_after="\n".join(context_after)
                            )
                            snippets.append(snippet)

                except Exception as e:
                    logger.warning(f"Failed to search file {file_path}: {e}")

        except Exception as e:
            logger.error(f"Python regex search failed: {e}")

        return snippets

    def _get_files_to_search(self, search_path: str, file_pattern: Optional[str]) -> List[str]:
        """
        获取需要搜索的文件列表

        Args:
            search_path: 搜索路径
            file_pattern: 文件模式

        Returns:
            List[str]: 文件路径列表
        """
        files = []

        if os.path.isfile(search_path):
            return [search_path]

        # 确定文件扩展名过滤器
        if file_pattern:
            # 将glob模式转换为扩展名列表
            if file_pattern.startswith('*.'):
                extensions = [file_pattern[1:]]  # 移除*
            else:
                extensions = ['.py', '.js', '.ts', '.java', '.cpp', '.c', '.h']  # 默认扩展名
        else:
            extensions = self.file_filter.local_include

        # 递归搜索文件
        for root, dirs, filenames in os.walk(search_path):
            for filename in filenames:
                file_path = os.path.join(root, filename)

                # 检查文件扩展名
                if any(filename.endswith(ext) for ext in extensions):
                    files.append(file_path)

        return files

    async def search_async(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        使用grep搜索代码片段的异步版本

        Args:
            query: 查询字符串，可以是XML格式或普通文本

        Returns:
            List[CodeSnippet]: 搜索结果
        """
        snippets = []

        try:
            # 解析查询参数
            search_params = self._parse_query(query)

            # 检查是否包含复杂正则表达式
            if self._is_complex_regex(search_params['regex']):
                logger.info("Using Python regex engine for complex pattern (async)")
                # 对于复杂正则表达式，在线程池中运行Python搜索
                loop = asyncio.get_event_loop()
                snippets = await loop.run_in_executor(None, self._python_regex_search, search_params)
            else:
                logger.info("Using grep for simple pattern (async)")
                # 构建grep命令
                cmd = self._build_grep_command(search_params)
                logger.info(f"CMD: {cmd}")

                # 异步执行搜索
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                )

                stdout, stderr = await process.communicate()

                if process.returncode == 0:
                    # 将bytes解码为字符串
                    stdout_str = stdout.decode('utf-8', errors='ignore')
                    snippets = self._parse_grep_output(stdout_str)
                elif process.returncode == 1:
                    # grep返回1表示没有找到匹配，这是正常情况
                    logger.info("No matches found")
                else:
                    # 将stderr解码为字符串用于日志记录
                    stderr_str = stderr.decode('utf-8', errors='ignore')
                    logger.warning(f"Grep command failed with return code {process.returncode}: {stderr_str}")

        except Exception as e:
            logger.error(f"Async grep search failed: {e}")

        return snippets

    def _parse_query(self, query: str) -> dict:
        """
        解析查询参数，支持XML格式和普通文本

        Args:
            query: 查询字符串

        Returns:
            dict: 解析后的参数字典
        """
        # 默认参数
        params = {
            'path': '.',  # 默认为当前目录，相对于repo_path
            'regex': query.strip(),
            'file_pattern': None
        }

        # 尝试解析XML格式
        query_stripped = query.strip()

        try:
            # 解析XML
            root = ET.fromstring(query_stripped)

            # 提取参数
            path_elem = root.find('path')
            if path_elem is not None and path_elem.text:
                # 处理相对路径
                path = path_elem.text.strip()
                params['path'] = path  # 保存原始路径，在build_command中处理

            regex_elem = root.find('regex')
            if regex_elem is not None and regex_elem.text:
                params['regex'] = regex_elem.text.strip()

            file_pattern_elem = root.find('file_pattern')
            if file_pattern_elem is not None and file_pattern_elem.text:
                params['file_pattern'] = file_pattern_elem.text.strip()

            logger.debug(f"Successfully parsed XML query: path={params['path']}, regex={params['regex']}, file_pattern={params['file_pattern']}")

        except ET.ParseError as e:
            logger.warning(f"Failed to parse XML query, using as plain text: {e}")
            logger.debug(f"Failed XML content: {query_stripped}")

        return params

    def _build_grep_command(self, params: dict) -> List[str]:
        """
        构建grep命令

        Args:
            params: 搜索参数

        Returns:
            List[str]: grep命令参数列表
        """
        cmd = [
            "grep",
            "-r",  # 递归搜索
            "-n",  # 显示行号
            "-E",  # 使用扩展正则表达式
            "-C", "3",  # 显示上下文3行
        ]

        # 处理正则表达式中的大小写不敏感标志
        regex = params['regex']
        if regex.startswith('(?i)'):
            cmd.append('-i')  # 忽略大小写
            regex = regex[4:]  # 移除(?i)前缀

        cmd.append(regex)

        # 处理搜索路径
        search_path = params['path']
        if search_path == '.':
            # 当前目录，使用repo_path
            final_path = self.repo_path
        elif os.path.isabs(search_path):
            # 绝对路径，直接使用
            final_path = search_path
        else:
            # 相对路径，相对于repo_path
            final_path = os.path.join(self.repo_path, search_path)

        cmd.append(final_path)

        # 添加文件模式过滤
        if params['file_pattern']:
            cmd.extend(["--include", params['file_pattern']])
        else:
            # 使用配置文件中的文件类型过滤
            for ext in self.file_filter.local_include:
                cmd.extend(["--include", f"*{ext}"])

        return cmd

    def _parse_grep_output(self, output: str) -> List[CodeSnippet]:
        """
        解析grep输出结果
        
        Args:
            output: grep命令输出
            
        Returns:
            List[CodeSnippet]: 解析后的代码片段
        """
        snippets = []
        current_file = None
        current_lines = []
        
        for line in output.split('\n'):
            if not line.strip() or line.strip() == '--':
                if current_file and current_lines:
                    snippets.extend(self._extract_snippets_from_lines(current_file, current_lines))
                    current_lines = []
                current_file = None
                continue
            
            # 解析grep输出格式：文件名:行号:内容 或 文件名-行号-内容（上下文）
            match = re.match(r'^([^:]+):(\d+):(.*)$', line)
            context_match = re.match(r'^([^:]+)-(\d+)-(.*)$', line)
            
            if match:
                file_path, line_number, content = match.groups()
                if not should_ignore_path(file_path):
                    current_file = file_path
                    current_lines.append((int(line_number), content, True))  # True表示匹配行
            elif context_match:
                file_path, line_number, content = context_match.groups()
                if current_file == file_path:
                    current_lines.append((int(line_number), content, False))  # False表示上下文行
        
        # 处理最后一组
        if current_file and current_lines:
            snippets.extend(self._extract_snippets_from_lines(current_file, current_lines))
        
        return snippets
    
    def _extract_snippets_from_lines(self, file_path: str, lines: List[Tuple[int, str, bool]]) -> List[CodeSnippet]:
        """
        从行数据中提取代码片段
        
        Args:
            file_path: 文件路径
            lines: 行数据列表 (行号, 内容, 是否匹配行)
            
        Returns:
            List[CodeSnippet]: 代码片段列表
        """
        snippets = []
        
        # 按匹配行分组
        i = 0
        while i < len(lines):
            line_number, content, is_match = lines[i]
            if is_match:
                # 收集上下文
                context_before = []
                context_after = []
                
                # 向前查找上下文
                j = i - 1
                while j >= 0 and not lines[j][2]:  # 上下文行
                    context_before.insert(0, lines[j][1])
                    j -= 1
                
                # 向后查找上下文
                j = i + 1
                while j < len(lines) and not lines[j][2]:  # 上下文行
                    context_after.append(lines[j][1])
                    j += 1
                
                snippet = CodeSnippet(
                    file_path=os.path.relpath(file_path, self.repo_path),
                    start_line=line_number,
                    end_line=line_number,
                    content=content,
                    context_before="\n".join(context_before),
                    context_after="\n".join(context_after)
                )
                snippets.append(snippet)
            
            i += 1
        
        return snippets

    @property
    def description(self):
        return """-`grep`: Perform a powerful regex-based search across multiple files within a directory tree. This tool recursively searches through files, finds pattern matches, and returns each match with surrounding context lines for better understanding. Ideal for code analysis, finding specific patterns, locating function definitions, or searching for text across large codebases.

Parameters:
- path: (required) The target directory path to search in, relative to the current working directory. The search will recursively include all subdirectories. Use "." for current directory.
- regex: (required) The regular expression pattern to search for. Uses extended grep regex syntax (POSIX ERE). Supports:
  - Basic patterns: `function`, `class MyClass`
  - Case-insensitive: `(?i)pattern` (special prefix handled by tool)
  - Word boundaries: `\\bword\\b`
  - Character classes: `[a-zA-Z]`, `[0-9]`
  - Quantifiers: `+`, `*`, `?`, `{n,m}`
  - Alternation: `pattern1|pattern2`
  - Grouping: `(pattern)`
- file_pattern: (optional) Glob pattern to filter which files to search. Examples:
  - `*.js` - only JavaScript files
  - `*.{ts,tsx}` - TypeScript files
  - `**/*.py` - Python files in all subdirectories
  - If omitted, uses configured file extensions

Note: Results will show each match with surrounding context lines. Large result sets may be truncated."""
    
    @property
    def examples(self):
        return """<output>
    <grep>
    <path>src</path>
    <regex>function\\s+(\\w+)</regex>
    <file_pattern>*.js</file_pattern>
    </grep>

    <grep>
    <path>.</path>
    <regex>(?i)todo|fixme</regex>
    </grep>
</output>
"""

if __name__ == "__main__":
    # 测试用例
    grep_search_tool = GrepSearchTool("/Users/<USER>/01-Projects/Codebase-Dev/backend/python/data/repos/PyMySQL")

    # 测试1: 简单正则表达式（使用grep）
    print("=== 测试1: 简单正则表达式 ===")
    query1 = "<grep><path>pymysql</path><regex>def executemany</regex><file_pattern>*.py</file_pattern></grep>"
    print(f"测试查询: {query1}")

    snippets1 = grep_search_tool.search(query1)
    print(f"找到 {len(snippets1)} 个结果")

    if snippets1:
        print("第一个结果:")
        print(f"文件: {snippets1[0].file_path}")
        print(f"行号: {snippets1[0].start_line}")
        print(f"内容: {snippets1[0].content}")

    print("\n=== 测试2: 复杂正则表达式（使用Python re） ===")
    # 测试2: 复杂正则表达式（使用Python re模块）
    query2 = "<grep><path>pymysql</path><regex>def\\s+\\w+.*?\\(.*?\\):</regex><file_pattern>*.py</file_pattern></grep>"
    print(f"测试查询: {query2}")

    snippets2 = grep_search_tool.search(query2)
    print(f"找到 {len(snippets2)} 个结果")

    if snippets2:
        print("前3个结果:")
        for i, snippet in enumerate(snippets2[:3]):
            print(f"{i+1}. 文件: {snippet.file_path}, 行号: {snippet.start_line}")
            print(f"   内容: {snippet.content}")

    print("\n=== 测试3: 非贪婪匹配 ===")
    # 测试3: 非贪婪匹配
    query3 = "<grep><path>pymysql</path><regex>class\\s+(\\w+?)\\(</regex><file_pattern>*.py</file_pattern></grep>"
    print(f"测试查询: {query3}")

    snippets3 = grep_search_tool.search(query3)
    print(f"找到 {len(snippets3)} 个结果")

    if snippets3:
        print("前3个结果:")
        for i, snippet in enumerate(snippets3[:3]):
            print(f"{i+1}. 文件: {snippet.file_path}, 行号: {snippet.start_line}")
            print(f"   内容: {snippet.content}")
