class ReadFile:
    @staticmethod
    def read_file(self, file_path: str, start_line: int, end_line: int) -> str:
        """
        读取文件内容
        
        Args:
            file_path: 文件路径
            start_line: 起始行号
            end_line: 结束行号
            
        Returns:
            str: 文件内容
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            return "\n".join(lines[start_line:end_line])
