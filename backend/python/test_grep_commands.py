#!/usr/bin/env python3
"""
测试不同的grep命令格式
"""

import subprocess
import os

def test_grep_commands():
    """测试不同的grep命令格式"""
    
    repo_path = "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/data/repos/PyMySQL"
    search_path = os.path.join(repo_path, "pymysql")
    
    # 测试用例
    test_cases = [
        {
            "name": "简单模式（无特殊字符）",
            "regex": "def executemany"
        },
        {
            "name": "复杂正则表达式（有特殊字符）",
            "regex": "def\\s+executemany\\s*\\(.*?\\)"
        },
        {
            "name": "包含括号的正则表达式",
            "regex": "def executemany\\("
        }
    ]
    
    for test_case in test_cases:
        print(f"\n=== {test_case['name']} ===")
        regex = test_case['regex']
        print(f"正则表达式: {regex}")
        
        # 方法1: 使用列表格式（当前方式）
        cmd_list = [
            "grep", "-r", "-n", "-E", "-C", "3",
            regex,
            search_path,
            "--include", "*.py"
        ]
        
        print(f"命令列表: {cmd_list}")
        print("执行结果（列表格式）:")
        
        try:
            result = subprocess.run(
                cmd_list,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                print(f"  找到 {len(lines)} 行结果")
                if lines and lines[0]:
                    print(f"  第一行: {lines[0]}")
            elif result.returncode == 1:
                print("  没有找到匹配")
            else:
                print(f"  错误 (返回码 {result.returncode}): {result.stderr}")
                
        except Exception as e:
            print(f"  异常: {e}")
        
        # 方法2: 使用shell字符串格式（对比）
        cmd_str = f'grep -r -n -E -C 3 "{regex}" "{search_path}" --include "*.py"'
        print(f"命令字符串: {cmd_str}")
        print("执行结果（字符串格式）:")
        
        try:
            result = subprocess.run(
                cmd_str,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                print(f"  找到 {len(lines)} 行结果")
                if lines and lines[0]:
                    print(f"  第一行: {lines[0]}")
            elif result.returncode == 1:
                print("  没有找到匹配")
            else:
                print(f"  错误 (返回码 {result.returncode}): {result.stderr}")
                
        except Exception as e:
            print(f"  异常: {e}")

if __name__ == "__main__":
    test_grep_commands()
